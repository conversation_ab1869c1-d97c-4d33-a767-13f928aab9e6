#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
周期处理模块

提供智能化的周期处理逻辑，包括周期检测、数据获取策略判断、
周期合成等功能，用于支持非原生周期数据的获取和处理。
"""

import os
import logging
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any

from utils.data_processor.period_converter import (
    is_supported_by_xtquant,
    get_supported_periods,
    get_recommended_base_period,
    convert_kline_period,
    validate_period_string,
    parse_period_to_minutes
)
from utils.logger import get_unified_logger
import re
from datetime import datetime

logger = get_unified_logger(__name__)


def convert_period_to_days(period: str) -> float:
    """
    将周期字符串转换为等效的天数

    Args:
        period: 周期字符串，如 '3m', '1h', '2d' 等

    Returns:
        float: 等效的天数，如果无法解析则返回0

    示例:
        '3m' -> 0.00208 (3分钟 = 3/1440天)
        '1h' -> 0.04167 (1小时 = 1/24天)
        '1d' -> 1.0 (1天)
        '1w' -> 7.0 (1周 = 7天)
        '1M' -> 30.0 (1月 ≈ 30天)
    """
    # 使用parse_period_to_minutes函数获取分钟数
    minutes = parse_period_to_minutes(period)
    
    # 转换为天数 (1天 = 24小时 = 1440分钟)
    if minutes > 0:
        return minutes / 1440.0
    
    # 特殊处理tick数据
    if period.lower() == 'tick':
        # tick数据可以视为接近于1分钟的粒度
        return 1.0 / 1440.0
    
    return 0.0


def get_period_type(period: str) -> str:
    """
    获取周期类型

    Args:
        period: 周期字符串，如 '3m', '1h', '2d', 'tick', '30s' 等

    Returns:
        str: 周期类型，可能的值: 'tick', 'second', 'minute', 'hour', 'day', 'week', 'month', 'unknown'
    """
    # 特殊处理tick数据
    if period.lower() == 'tick':
        return 'tick'
    
    # 验证周期格式
    if not validate_period_string(period):
        return 'unknown'
    
    # 使用正则表达式解析周期
    pattern = r'^(\d+)(m|h|d|w|M|s)$'
    match = re.match(pattern, period)
    
    if not match:
        return 'unknown'
    
    # 获取单位
    unit = match.group(2)
    
    # 根据单位返回类型
    if unit == 's':
        return 'second'
    elif unit == 'm':
        return 'minute'
    elif unit == 'h':
        return 'hour'
    elif unit == 'd':
        return 'day'
    elif unit == 'w':
        return 'week'
    elif unit == 'M':
        return 'month'
    
    return 'unknown'


def get_native_periods() -> List[str]:
    """
    获取系统原生支持的周期列表

    Returns:
        List[str]: 原生支持的周期列表，如 ['tick', '1m', '5m', '15m', '30m', '1h', '1d']
    """
    # 从period_converter模块获取迅投API支持的周期
    xtquant_periods = get_supported_periods()

    # 添加'tick'周期，它是系统支持但不在迅投标准周期列表中的特殊周期
    native_periods = ['tick'] + xtquant_periods

    return native_periods


def is_valid_period(period: str) -> bool:
    """
    检查周期字符串是否有效（包括自定义周期）

    Args:
        period: 周期字符串

    Returns:
        bool: 如果周期格式有效返回True，否则返回False
    """
    # 检查是否为原生支持的周期
    if period in get_native_periods():
        return True

    # 检查是否为有效的自定义周期格式
    return validate_period_string(period)


def is_custom_period(period: str) -> bool:
    """
    判断是否为自定义周期（非原生支持）

    Args:
        period: 周期字符串

    Returns:
        bool: 是否为自定义周期
    """
    # tick数据不是自定义周期，需要直接从数据源获取
    if period.lower() == 'tick':
        return False

    # 先验证周期字符串格式
    if not validate_period_string(period):
        return False

    # 检查是否为非原生支持周期
    return not is_supported_by_xtquant(period)


def get_synthesis_strategy(
    period: str,
    symbol: str,
    data_root: str,
    start_time: str,
    end_time: str
) -> Dict[str, Any]:
    """
    获取周期合成策略

    根据目标周期和已有数据情况，确定最佳的数据获取和周期合成策略

    Args:
        period: 目标周期
        symbol: 股票代码
        data_root: 数据根目录
        start_time: 开始时间
        end_time: 结束时间

    Returns:
        Dict[str, Any]: 策略信息字典，包含:
            - need_download: 是否需要下载基础数据
            - base_period: 基础周期
            - local_data_sufficient: 本地基础数据是否充足
            - local_data_path: 本地基础数据路径（如果存在）
            - local_data_range: 本地基础数据范围（如果存在）
    """
    from data.storage.path_manager import get_save_path
    from utils.data_processor.data_merger import get_data_time_range

    # 获取推荐的基础周期（目前都是1m）
    base_period = get_recommended_base_period(period)

    # 构造结果字典
    result = {
        "need_download": True,  # 默认需要下载
        "base_period": base_period,
        "local_data_sufficient": False,
        "local_data_path": None,
        "local_data_range": None
    }

    # 检查本地是否有基础周期数据
    local_data_path = get_save_path(data_root, symbol, base_period, start_time[:8] if start_time else None)
    result["local_data_path"] = local_data_path

    if os.path.exists(local_data_path):
        try:
            # 读取本地数据
            local_data = pd.read_parquet(local_data_path)

            if not local_data.empty:
                # 获取本地数据的时间范围
                local_start, local_end = get_data_time_range(local_data)

                if local_start and local_end:
                    # 记录数据范围
                    result["local_data_range"] = (local_start, local_end)

                    # 格式化为字符串进行比较
                    local_start_str = local_start.strftime("%Y%m%d")
                    local_end_str = local_end.strftime("%Y%m%d")

                    # 检查本地数据是否覆盖请求的时间范围
                    # 注意：这里只比较日期部分
                    if local_start_str <= start_time[:8] and local_end_str >= end_time[:8]:
                        result["local_data_sufficient"] = True
                        result["need_download"] = False
                        logger.info(
                            f"{symbol} 本地基础周期数据已覆盖请求的时间范围 ({start_time} 到 {end_time})")
        except Exception as e:
            logger.warning(f"检查本地数据时出错: {e}")

    return result


def synthesize_period_data(
    base_data: pd.DataFrame,
    target_period: str,
    source_period: str = '1m',
    symbol: str = ""
) -> pd.DataFrame:
    """
    合成指定周期的数据

    Args:
        base_data: 基础数据
        target_period: 目标周期
        source_period: 源数据周期类型，可以是'1m'、'tick'等
        symbol: 股票代码，用于品种识别和交易时间判断

    Returns:
        pd.DataFrame: 合成后的数据
    """
    return convert_kline_period(base_data, target_period, source_period, symbol)


def synthesize_from_local_data(
    symbols: List[str],
    source_period: str,
    target_period: str,
    start_time: str,
    end_time: str,
    data_dir: Optional[str] = None
) -> Dict[str, Any]:
    """
    从本地数据合成新周期数据

    修改历史：
    - 2025-07-12: 重构数据读取逻辑，使用read_partitioned_data统一处理分区数据
                  改进错误处理和日志记录，使用ParquetStorage统一保存数据

    Args:
        symbols: 股票代码列表
        source_period: 源数据周期
        target_period: 目标周期
        start_time: 开始时间，格式为 'YYYYMMDD' 或 'YYYYMMDDHHMMSS'
        end_time: 结束时间，格式为 'YYYYMMDD' 或 'YYYYMMDDHHMMSS'
        data_dir: 数据目录，默认为None使用系统默认目录

    Returns:
        Dict[str, Any]: 结果字典，包含:
            - success: 是否至少有一个股票成功合成
            - successful_symbols: 成功合成的股票列表
            - failed_symbols: 合成失败的股票列表
            - data: 股票代码到DataFrame的映射
            - save_paths: 股票代码到保存路径的映射
            - failed_reasons: 失败原因字典
    """
    from data.storage.path_manager import get_save_path
    from utils.logger import get_unified_logger
    from config.settings import DATA_ROOT
    import os

    # 初始化日志记录器
    logger = get_unified_logger(__name__)

    # 使用指定数据目录或默认目录
    data_root = data_dir if data_dir is not None else DATA_ROOT

    # 初始化结果字典
    result = {
        "success": False,
        "successful_symbols": [],
        "failed_symbols": [],
        "data": {},
        "save_paths": {},
        "failed_reasons": {}
    }

    # 获取股票总数以显示进度
    total_stocks = len(symbols)
    logger.info(f"准备为 {total_stocks} 个股票合成 {target_period} 数据\n")

    # 性能监控初始化
    import time
    start_time_total = time.time()
    incremental_count = 0
    full_synthesis_count = 0
    trading_calendar_success_count = 0
    trading_calendar_fallback_count = 0

    # 逐个处理股票
    for i, symbol in enumerate(symbols):
        logger.info(f"[{i+1}/{total_stocks}] 处理: {symbol}")

        # 单个股票处理开始时间
        start_time_single = time.time()

        try:
            # 增量更新检测逻辑
            from data.storage.parquet_reader import read_partitioned_data, get_latest_partition_file, read_latest_data_timestamp
            from utils.data_processor.data_merger import get_data_time_range, calculate_incremental_start_time
            from utils.calendar.trading_calendar import calculate_overlap_with_fallback, get_market_from_symbol

            # 检测已有合成数据
            latest_file = get_latest_partition_file(data_root, symbol, target_period)
            actual_start_time = start_time
            is_incremental = False

            if latest_file and os.path.exists(latest_file):
                try:
                    latest_timestamp = read_latest_data_timestamp(latest_file)
                    if latest_timestamp:
                        logger.info(f"{symbol} 检测到已有合成数据，最新时间戳: {latest_timestamp}")

                        # 判断是否需要增量更新
                        if latest_timestamp >= start_time:
                            logger.info(f"{symbol} 已有数据包含请求范围，启用增量更新模式")

                            # 使用基于交易日历的重叠计算
                            try:
                                # 根据目标周期确定重叠交易日数
                                if target_period == '1m':
                                    overlap_trading_days = 1  # 1m使用1个交易日重叠
                                elif target_period in ['5m', '15m', '30m']:
                                    overlap_trading_days = 2  # 小时内周期使用2个交易日
                                elif target_period in ['1h', '2h', '4h']:
                                    overlap_trading_days = 3  # 日内周期使用3个交易日
                                else:
                                    overlap_trading_days = 5  # 日级以上使用5个交易日

                                # 使用动态重叠计算
                                from utils.calendar.trading_calendar import calculate_dynamic_overlap

                                overlap_start = calculate_dynamic_overlap(
                                    end_date=latest_timestamp[:8],  # 转换为YYYYMMDD格式
                                    target_period=target_period,
                                    symbol=symbol
                                )

                                # 增量更新逻辑修正：只要有重叠计算结果就使用增量模式
                                # 重叠起始时间可能晚于请求时间，这是正常的增量更新场景
                                actual_start_time = overlap_start
                                is_incremental = True
                                incremental_count += 1
                                trading_calendar_success_count += 1

                                if overlap_start < start_time:
                                    logger.info(f"{symbol} 启用增量合成模式，重叠起始时间: {overlap_start} 早于请求时间 {start_time} (重叠{overlap_trading_days}个交易日)")
                                else:
                                    logger.info(f"{symbol} 启用增量合成模式，重叠起始时间: {overlap_start} 晚于请求时间 {start_time}，从重叠点开始合成 (重叠{overlap_trading_days}个交易日)")

                            except Exception as overlap_error:
                                trading_calendar_fallback_count += 1
                                full_synthesis_count += 1
                                logger.warning(f"{symbol} 交易日历重叠计算失败: {overlap_error}，使用全量合成")
                        else:
                            full_synthesis_count += 1
                            logger.info(f"{symbol} 已有数据时间({latest_timestamp})早于请求时间({start_time})，使用全量合成")

                except Exception as detection_error:
                    logger.warning(f"{symbol} 增量更新检测失败: {detection_error}，使用全量合成")
                    full_synthesis_count += 1
            else:
                logger.info(f"{symbol} 未检测到已有合成数据，使用全量合成")
                full_synthesis_count += 1

            # 使用统一的分区数据读取函数
            logger.debug(f"{symbol} 开始读取 {source_period} 数据，时间范围: {actual_start_time} 至 {end_time}")
            if is_incremental:
                logger.info(f"{symbol} 增量模式: 处理时间范围 {actual_start_time} 至 {end_time} (包含重叠数据)")

            # 使用优化的精确时间范围数据读取
            from data.storage.parquet_reader import load_data_by_time_range

            # 记录交易日历使用情况
            if is_incremental:
                trading_calendar_success_count += 1

            source_data = load_data_by_time_range(
                data_root=data_root,
                symbol=symbol,
                period=source_period,
                start_time=actual_start_time,
                end_time=end_time,
                validate_trading_time=True  # 启用交易时间验证
            )

            if source_data is None or source_data.empty:
                error_msg = f"指定时间范围内 {source_period} 数据为空或不存在"
                logger.error(f"{symbol} {error_msg}")
                logger.debug(f"{symbol} 数据读取详情: 数据根目录={data_root}, 周期={source_period}, 时间范围={start_time}至{end_time}")
                result["failed_symbols"].append(symbol)
                result["failed_reasons"][symbol] = error_msg
                continue

            logger.info(f"{symbol} 成功读取 {source_period} 源数据: {len(source_data)} 行\n {source_data}")

            # read_partitioned_data已经应用了时间范围过滤，直接使用返回的数据
            filtered_data = source_data

            # 获取过滤后数据的时间范围
            data_start, data_end = get_data_time_range(filtered_data)
            logger.debug(f"{symbol} 源数据时间范围: {data_start} 至 {data_end}")

            # 合成目标周期数据
            logger.debug(f"{symbol} 开始合成 {target_period} 数据，源数据行数: {len(filtered_data)}")
            synthesized_data = synthesize_period_data(filtered_data, target_period, source_period, symbol)

            if synthesized_data is None or synthesized_data.empty:
                error_msg = f"合成 {target_period} 数据失败，结果为空"
                logger.error(f"{symbol} {error_msg}")
                logger.debug(f"{symbol} 合成失败详情: 源周期={source_period}, 目标周期={target_period}, 源数据行数={len(filtered_data)}")
                result["failed_symbols"].append(symbol)
                result["failed_reasons"][symbol] = error_msg
                continue

            logger.info(f"{symbol} 成功合成 {target_period} 数据: {len(synthesized_data)} 行\n {synthesized_data}")

            # 过滤首条可能不完整的数据，确保历史数据质量
            if len(synthesized_data) > 1:
                original_count = len(synthesized_data)
                synthesized_data = synthesized_data.iloc[1:]  # 删除首条数据
                logger.info(f"{symbol} 已过滤首条不完整数据，从 {original_count} 行减少到 {len(synthesized_data)} 行")
            else:
                logger.warning(f"{symbol} 合成数据只有 {len(synthesized_data)} 行，无法过滤首条数据")

            # 保存合成的数据 - 使用统一的分区存储方式
            logger.debug(f"{symbol} 开始保存合成的 {target_period} 数据")

            try:
                from data.storage.parquet_storage import ParquetStorage
                storage = ParquetStorage(base_dir=data_root)

                # 根据是否为增量模式选择存储方式
                if is_incremental:
                    # 使用增量更新存储
                    save_success = storage.incremental_update(
                        dataframe=synthesized_data,
                        symbol=symbol,
                        period=target_period
                    )
                    logger.info(f"{symbol} 使用增量更新存储模式")
                else:
                    # 使用全量存储
                    save_success = storage.save_data_by_partition_parallel(
                        dataframe=synthesized_data,
                        symbol=symbol,
                        period=target_period
                    )
                    logger.info(f"{symbol} 使用全量存储模式")

                if not save_success:
                    error_msg = f"保存合成的 {target_period} 数据失败"
                    logger.error(f"{symbol} {error_msg}")
                    result["failed_symbols"].append(symbol)
                    result["failed_reasons"][symbol] = error_msg
                    continue

                # 获取保存路径用于记录
                timestamp = extract_timestamp_from_data(synthesized_data, end_time)
                save_path = get_save_path(data_root, symbol, target_period, timestamp)

                logger.info(f"{symbol} 成功保存合成数据到分区存储")

                # 更新结果
                result["successful_symbols"].append(symbol)
                result["data"][symbol] = synthesized_data
                result["save_paths"][symbol] = save_path

            except Exception as save_error:
                error_msg = f"保存数据时出错: {save_error}"
                logger.error(f"{symbol} {error_msg}")
                result["failed_symbols"].append(symbol)
                result["failed_reasons"][symbol] = error_msg
                continue

            data_start_str = "未知" if data_start is None else data_start.strftime(
                "%Y-%m-%d %H:%M:%S")
            data_end_str = "未知" if data_end is None else data_end.strftime(
                "%Y-%m-%d %H:%M:%S")

            logger.info(
                f"{symbol} 周期合成成功，从 {len(filtered_data)} 行 {source_period} 数据 ({data_start_str} 至 {data_end_str}) 合成了 {len(synthesized_data)} 行 {target_period} 数据：{synthesized_data}\n")

        except Exception as e:
            import traceback
            error_msg = str(e)
            logger.error(f"处理 {symbol} 时出错: {error_msg}")
            logger.debug(traceback.format_exc())
            result["failed_symbols"].append(symbol)
            result["failed_reasons"][symbol] = error_msg

    # 更新结果状态
    result["success"] = len(result["successful_symbols"]) > 0

    # 性能监控总结
    end_time_total = time.time()
    total_duration = end_time_total - start_time_total

    logger.info("=" * 60)
    logger.info("📊 合成任务性能统计报告")
    logger.info("=" * 60)
    logger.info(f"总处理时间: {total_duration:.2f}秒")
    logger.info(f"处理股票数量: {total_stocks}个")
    logger.info(f"成功处理: {len(result['successful_symbols'])}个")
    logger.info(f"失败处理: {len(result['failed_symbols'])}个")
    logger.info(f"平均每股处理时间: {total_duration/total_stocks:.2f}秒")
    logger.info("-" * 40)
    logger.info("🚀 增量更新统计:")
    logger.info(f"增量合成: {incremental_count}个 ({incremental_count/total_stocks*100:.1f}%)")
    logger.info(f"全量合成: {full_synthesis_count}个 ({full_synthesis_count/total_stocks*100:.1f}%)")
    if incremental_count > 0:
        efficiency_improvement = (full_synthesis_count + incremental_count) / max(full_synthesis_count, 1)
        logger.info(f"预估性能提升: {efficiency_improvement:.1f}x")
    logger.info("-" * 40)
    logger.info("📅 交易日历统计:")
    logger.info(f"交易日历成功: {trading_calendar_success_count}个")
    logger.info(f"交易日历回退: {trading_calendar_fallback_count}个")
    if trading_calendar_success_count + trading_calendar_fallback_count > 0:
        calendar_success_rate = trading_calendar_success_count / (trading_calendar_success_count + trading_calendar_fallback_count) * 100
        logger.info(f"交易日历成功率: {calendar_success_rate:.1f}%")
    logger.info("=" * 60)

    return result


# 添加通用的提取时间戳函数
def extract_timestamp_from_data(df: pd.DataFrame, default_time: Optional[str] = None) -> Optional[str]:
    """
    从DataFrame中提取时间戳（使用统一日期提取模块）

    尝试从数据中提取日期时间戳，用于生成正确的分区存储路径

    Args:
        df: 数据DataFrame
        default_time: 如果从数据中提取失败，使用的备选时间戳

    Returns:
        Optional[str]: 格式为'YYYYMMDD'的时间戳，如果提取失败则返回None
    """
    # 使用统一日期提取模块
    from utils.time_formatter.date_extraction import extract_timestamp_from_data as unified_extract

    result = unified_extract(df, default_time)

    # 确保返回格式为YYYYMMDD（兼容原有接口）
    if result and len(result) >= 8:
        return result[:8]

    return result
